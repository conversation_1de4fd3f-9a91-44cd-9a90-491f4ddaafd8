#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
训练进程终止工具

用于查找和终止正在运行的训练进程。

使用方法:
    python 终止训练进程.py                    # 查看所有训练进程
    python 终止训练进程.py --kill-all        # 终止所有训练进程
    python 终止训练进程.py --pid 12345       # 终止指定PID的进程
"""

import os
import sys
import psutil
import argparse
import signal
from typing import List, Dict, Any


def find_training_processes() -> List[Dict[str, Any]]:
    """查找所有训练相关的进程"""
    training_processes = []
    
    # 训练相关的关键词
    training_keywords = [
        'main_training.py',
        'efficient_zero',
        'muzero',
        'doudizhu',
        'training',
        'cardgame_ai'
    ]
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cpu_percent', 'memory_info']):
            try:
                proc_info = proc.info
                cmdline = proc_info.get('cmdline', [])
                
                if not cmdline:
                    continue
                
                # 检查命令行是否包含训练相关关键词
                cmdline_str = ' '.join(cmdline).lower()
                
                for keyword in training_keywords:
                    if keyword in cmdline_str:
                        # 获取更多进程信息
                        try:
                            cpu_percent = proc.cpu_percent()
                            memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                            
                            training_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'cmdline': cmdline,
                                'cmdline_str': ' '.join(cmdline),
                                'create_time': proc_info['create_time'],
                                'cpu_percent': cpu_percent,
                                'memory_mb': memory_mb,
                                'keyword_matched': keyword
                            })
                            break  # 找到一个关键词就够了
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                            
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        print(f"❌ 搜索进程时出错: {e}")
    
    return training_processes


def display_processes(processes: List[Dict[str, Any]]):
    """显示进程信息"""
    if not processes:
        print("✅ 未找到正在运行的训练进程")
        return
    
    print(f"🔍 找到 {len(processes)} 个训练相关进程:")
    print("=" * 80)
    
    for i, proc in enumerate(processes, 1):
        from datetime import datetime
        create_time = datetime.fromtimestamp(proc['create_time'])
        
        print(f"[{i}] PID: {proc['pid']}")
        print(f"    名称: {proc['name']}")
        print(f"    命令: {proc['cmdline_str'][:100]}{'...' if len(proc['cmdline_str']) > 100 else ''}")
        print(f"    启动时间: {create_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    CPU使用率: {proc['cpu_percent']:.1f}%")
        print(f"    内存使用: {proc['memory_mb']:.1f} MB")
        print(f"    匹配关键词: {proc['keyword_matched']}")
        print()


def terminate_process(pid: int, force: bool = False) -> bool:
    """终止指定进程"""
    try:
        proc = psutil.Process(pid)
        proc_name = proc.name()
        
        print(f"⏹️ 正在终止进程 PID: {pid} ({proc_name})")
        
        if force:
            # 强制终止
            proc.kill()
            print(f"💀 已强制终止进程 {pid}")
        else:
            # 优雅终止
            proc.terminate()
            
            # 等待进程结束
            try:
                proc.wait(timeout=5)
                print(f"✅ 进程 {pid} 已优雅终止")
            except psutil.TimeoutExpired:
                print(f"⚠️ 进程 {pid} 未响应，强制终止...")
                proc.kill()
                proc.wait()
                print(f"💀 进程 {pid} 已强制终止")
        
        return True
        
    except psutil.NoSuchProcess:
        print(f"❌ 进程 {pid} 不存在")
        return False
    except psutil.AccessDenied:
        print(f"❌ 没有权限终止进程 {pid}")
        return False
    except Exception as e:
        print(f"❌ 终止进程 {pid} 时出错: {e}")
        return False


def terminate_all_training_processes(force: bool = False) -> int:
    """终止所有训练进程"""
    processes = find_training_processes()
    
    if not processes:
        print("✅ 没有找到需要终止的训练进程")
        return 0
    
    print(f"🔄 准备终止 {len(processes)} 个训练进程...")
    
    terminated_count = 0
    for proc in processes:
        if terminate_process(proc['pid'], force):
            terminated_count += 1
    
    print(f"✅ 已终止 {terminated_count}/{len(processes)} 个进程")
    return terminated_count


def interactive_terminate():
    """交互式终止进程"""
    processes = find_training_processes()
    
    if not processes:
        print("✅ 没有找到正在运行的训练进程")
        return
    
    display_processes(processes)
    
    while True:
        try:
            choice = input("\n请选择操作:\n"
                          "  输入进程编号 (1-{}) 终止单个进程\n"
                          "  输入 'all' 终止所有进程\n"
                          "  输入 'q' 退出\n"
                          "选择: ".format(len(processes)))
            
            if choice.lower() == 'q':
                print("👋 退出")
                break
            elif choice.lower() == 'all':
                confirm = input("⚠️ 确认终止所有训练进程? (y/N): ")
                if confirm.lower() == 'y':
                    terminate_all_training_processes()
                break
            else:
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(processes):
                        proc = processes[index]
                        confirm = input(f"⚠️ 确认终止进程 {proc['pid']} ({proc['name']})? (y/N): ")
                        if confirm.lower() == 'y':
                            terminate_process(proc['pid'])
                    else:
                        print("❌ 无效的进程编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
        except KeyboardInterrupt:
            print("\n👋 退出")
            break


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="训练进程终止工具")
    parser.add_argument('--list', action='store_true', help='列出所有训练进程')
    parser.add_argument('--kill-all', action='store_true', help='终止所有训练进程')
    parser.add_argument('--pid', type=int, help='终止指定PID的进程')
    parser.add_argument('--force', action='store_true', help='强制终止进程')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    print("🔧 训练进程管理工具")
    print("=" * 40)
    
    if args.list:
        # 列出进程
        processes = find_training_processes()
        display_processes(processes)
        
    elif args.kill_all:
        # 终止所有进程
        terminate_all_training_processes(args.force)
        
    elif args.pid:
        # 终止指定进程
        terminate_process(args.pid, args.force)
        
    elif args.interactive:
        # 交互式模式
        interactive_terminate()
        
    else:
        # 默认：显示进程并提供选择
        processes = find_training_processes()
        
        if not processes:
            print("✅ 没有找到正在运行的训练进程")
            print("\n💡 如果训练进程仍在运行，可以尝试:")
            print("   python 终止训练进程.py --interactive")
            return
        
        display_processes(processes)
        
        print("💡 使用以下命令进行操作:")
        print("   python 终止训练进程.py --kill-all     # 终止所有训练进程")
        print("   python 终止训练进程.py --interactive  # 交互式选择")
        print("   python 终止训练进程.py --pid <PID>    # 终止指定进程")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
