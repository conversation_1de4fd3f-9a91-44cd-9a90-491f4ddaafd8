"""
专家策略池模块

提供多种策略"专家"的管理和调度功能，包括基于规则的策略、基于MCTS的策略、
基于深度RL的策略（多个变种）、训练好的人类策略等。
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Type
import numpy as np

from cardgame_ai.core.policy import Policy
from cardgame_ai.core.base import State, Action

# 配置日志
logger = logging.getLogger(__name__)


class ExpertPolicyPool:
    """
    专家策略池

    管理多种策略"专家"，提供统一的接口进行访问和调度。
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化专家策略池

        Args:
            config_path: 配置文件路径，用于加载专家策略配置
        """
        self.experts: Dict[str, Policy] = {}
        self.expert_metadata: Dict[str, Dict[str, Any]] = {}
        self.config_path = config_path

        # 加载专家策略
        self._load_experts()

    def _load_experts(self) -> None:
        """
        加载专家策略

        从配置文件或默认设置加载专家策略。
        """
        # 如果提供了配置文件，从配置文件加载
        if self.config_path and os.path.exists(self.config_path):
            self._load_from_config()
        else:
            # 否则加载默认专家
            self._load_default_experts()

        logger.info(f"已加载 {len(self.experts)} 个专家策略: {list(self.experts.keys())}")

    def _load_from_config(self) -> None:
        """
        从配置文件加载专家策略
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载每个专家策略
            for expert_name, expert_config in config.get('experts', {}).items():
                try:
                    # 获取策略类型
                    policy_type = expert_config.get('type')
                    policy_path = expert_config.get('path')
                    policy_params = expert_config.get('params', {})

                    # 创建策略实例
                    policy = self._create_policy(policy_type, policy_path, policy_params)

                    if policy:
                        # 添加到专家池
                        self.experts[expert_name] = policy

                        # 保存元数据
                        self.expert_metadata[expert_name] = {
                            'type': policy_type,
                            'path': policy_path,
                            'params': policy_params,
                            'description': expert_config.get('description', ''),
                            'tags': expert_config.get('tags', []),
                            'performance': expert_config.get('performance', {}),
                            'created_at': expert_config.get('created_at', ''),
                            'updated_at': expert_config.get('updated_at', '')
                        }

                        logger.info(f"已加载专家策略 '{expert_name}' (类型: {policy_type})")
                    else:
                        logger.warning(f"无法创建专家策略 '{expert_name}' (类型: {policy_type})")

                except Exception as e:
                    logger.error(f"加载专家策略 '{expert_name}' 时出错: {e}")

        except Exception as e:
            logger.error(f"加载配置文件 '{self.config_path}' 时出错: {e}")
            # 加载默认专家作为备选
            self._load_default_experts()

    def _create_policy(self, policy_type: str, policy_path: Optional[str] = None,
                      policy_params: Optional[Dict[str, Any]] = None) -> Optional[Policy]:
        """
        创建策略实例

        Args:
            policy_type: 策略类型
            policy_path: 策略模型路径
            policy_params: 策略参数

        Returns:
            策略实例，如果创建失败则返回None
        """
        if policy_params is None:
            policy_params = {}

        try:
            # 根据策略类型创建相应的策略实例
            if policy_type == 'random':
                # 随机策略
                from cardgame_ai.core.policy import RandomPolicy
                return RandomPolicy(name=policy_params.get('name', 'random_policy'))

            elif policy_type == 'rule_based':
                # 基于规则的策略
                try:
                    from cardgame_ai.algorithms.rule_based import RuleBasedAgent
                    # RuleBasedAgent不接受name参数，过滤掉name参数
                    filtered_params = {k: v for k, v in policy_params.items() if k != 'name'}
                    return RuleBasedAgent(**filtered_params)
                except ImportError:
                    logger.warning("无法导入RuleBasedAgent，请确保已安装相关依赖")
                    return None

            elif policy_type == 'mcts':
                # 基于MCTS的策略
                try:
                    from cardgame_ai.algorithms.mcts_agent import MCTSAgent
                    from cardgame_ai.models.value_policy_net import ValuePolicyNet

                    # 创建模型（如果没有提供）
                    if 'model' not in policy_params:
                        model = ValuePolicyNet(
                            input_dim=policy_params.get('input_dim', 108),
                            hidden_dim=policy_params.get('hidden_dim', 256),
                            action_dim=policy_params.get('action_dim', 310)
                        )
                        policy_params['model'] = model

                    # MCTSAgent不接受name参数，过滤掉name参数
                    filtered_params = {k: v for k, v in policy_params.items() if k != 'name'}
                    return MCTSAgent(**filtered_params)
                except ImportError:
                    logger.warning("无法导入MCTSAgent，请确保已安装相关依赖")
                    return None

            elif policy_type == 'dqn':
                # 基于DQN的策略
                try:
                    from cardgame_ai.algorithms.dqn import DQN
                    policy = DQN(**policy_params)
                    if policy_path and os.path.exists(policy_path):
                        policy.load(policy_path)
                    return policy
                except ImportError:
                    logger.warning("无法导入DQN，请确保已安装相关依赖")
                    return None

            elif policy_type == 'ppo':
                # 基于PPO的策略
                try:
                    from cardgame_ai.algorithms.ppo import PPO
                    policy = PPO(**policy_params)
                    if policy_path and os.path.exists(policy_path):
                        policy.load(policy_path)
                    return policy
                except ImportError:
                    logger.warning("无法导入PPO，请确保已安装相关依赖")
                    return None

            elif policy_type == 'muzero':
                # 基于MuZero的策略
                try:
                    from cardgame_ai.algorithms.muzero import MuZero
                    policy = MuZero(**policy_params)
                    if policy_path and os.path.exists(policy_path):
                        policy.load(policy_path)
                    return policy
                except ImportError:
                    logger.warning("无法导入MuZero，请确保已安装相关依赖")
                    return None

            elif policy_type == 'efficient_zero':
                # 基于EfficientZero的策略
                try:
                    from cardgame_ai.algorithms.efficient_zero import EfficientZero
                    policy = EfficientZero(**policy_params)
                    if policy_path and os.path.exists(policy_path):
                        policy.load(policy_path)
                    return policy
                except ImportError:
                    logger.warning("无法导入EfficientZero，请确保已安装相关依赖")
                    return None

            elif policy_type == 'transformer':
                # 基于Transformer的策略
                try:
                    from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
                    policy = TransformerPolicy(**policy_params)
                    if policy_path and os.path.exists(policy_path):
                        policy.load(policy_path)
                    return policy
                except ImportError:
                    logger.warning("无法导入TransformerPolicy，请确保已安装相关依赖")
                    return None

            else:
                logger.warning(f"未知的策略类型: {policy_type}")
                return None

        except Exception as e:
            logger.error(f"创建策略 '{policy_type}' 时出错: {e}")
            return None

    def _load_default_experts(self) -> None:
        """
        加载默认专家策略
        """
        # 清空现有专家
        self.experts.clear()
        self.expert_metadata.clear()

        # 添加随机策略
        try:
            from cardgame_ai.core.policy import RandomPolicy
            self.experts['random'] = RandomPolicy(name='random_policy')
            self.expert_metadata['random'] = {
                'type': 'random',
                'description': '完全随机的策略，用于基准测试',
                'tags': ['baseline', 'random'],
                'performance': {'win_rate': 0.0}
            }
            logger.info("已加载默认专家策略 'random'")
        except Exception as e:
            logger.error(f"加载随机策略时出错: {e}")

        # 添加基于规则的策略
        try:
            from cardgame_ai.algorithms.rule_based import RuleBasedAgent
            # RuleBasedAgent不接受name参数，使用默认参数
            self.experts['rule_based'] = RuleBasedAgent(difficulty='medium')
            self.expert_metadata['rule_based'] = {
                'type': 'rule_based',
                'description': '基于人工规则的策略，具有基本的游戏策略',
                'tags': ['heuristic', 'rule_based'],
                'performance': {'win_rate': 0.3}
            }
            logger.info("已加载默认专家策略 'rule_based'")
        except Exception as e:
            logger.error(f"加载基于规则的策略时出错: {e}")

        # 尝试加载MCTS策略
        try:
            from cardgame_ai.algorithms.mcts_agent import MCTSAgent
            from cardgame_ai.models.value_policy_net import ValuePolicyNet

            # 创建一个简单的价值-策略网络作为MCTS的模型
            model = ValuePolicyNet(input_dim=108, hidden_dim=256, action_dim=310)

            # MCTSAgent不接受name参数，只传入必要参数
            self.experts['mcts_basic'] = MCTSAgent(
                model=model,
                num_simulations=50,
                enable_logging=False
            )
            self.expert_metadata['mcts_basic'] = {
                'type': 'mcts',
                'description': '基础MCTS策略，使用50次模拟',
                'tags': ['search', 'mcts', 'basic'],
                'performance': {'win_rate': 0.5}
            }
            logger.info("已加载默认专家策略 'mcts_basic'")
        except Exception as e:
            logger.warning(f"MCTS策略暂时不可用: {e}")
            # 不记录为ERROR，因为这不是关键错误

        # 尝试加载预训练模型（如果存在）
        model_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'models')
        if os.path.exists(model_dir):
            self._load_pretrained_models(model_dir)

    def _load_pretrained_models(self, model_dir: str) -> None:
        """
        加载预训练模型

        Args:
            model_dir: 模型目录
        """
        # 遍历模型目录
        for model_type in os.listdir(model_dir):
            type_dir = os.path.join(model_dir, model_type)
            if not os.path.isdir(type_dir):
                continue

            # 遍历模型类型目录
            for model_name in os.listdir(type_dir):
                model_path = os.path.join(type_dir, model_name)
                if not os.path.isdir(model_path):
                    continue

                # 检查模型文件
                if not any(f.endswith('.pth') or f.endswith('.pt') or f.endswith('.ckpt')
                          for f in os.listdir(model_path)):
                    continue

                # 尝试加载模型
                try:
                    # 构建专家名称
                    expert_name = f"{model_type}_{model_name}"

                    # 创建策略实例
                    policy = self._create_policy(model_type, model_path, {})

                    if policy:
                        # 添加到专家池
                        self.experts[expert_name] = policy

                        # 保存元数据
                        self.expert_metadata[expert_name] = {
                            'type': model_type,
                            'path': model_path,
                            'description': f'预训练的{model_type}模型 ({model_name})',
                            'tags': ['pretrained', model_type],
                            'performance': {}
                        }

                        logger.info(f"已加载预训练模型 '{expert_name}'")

                except Exception as e:
                    logger.error(f"加载预训练模型 '{model_type}/{model_name}' 时出错: {e}")

    def get_expert(self, name: str) -> Optional[Policy]:
        """
        获取指定名称的专家策略

        Args:
            name: 专家策略名称

        Returns:
            专家策略实例，如果不存在则返回None
        """
        return self.experts.get(name)

    def list_experts(self) -> List[str]:
        """
        列出所有专家策略名称

        Returns:
            专家策略名称列表
        """
        return list(self.experts.keys())

    def get_all_experts(self) -> Dict[str, Policy]:
        """
        获取所有专家策略

        Returns:
            专家策略字典，键为名称，值为策略实例
        """
        return self.experts.copy()

    def get_expert_metadata(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定专家策略的元数据

        Args:
            name: 专家策略名称

        Returns:
            专家策略元数据，如果不存在则返回None
        """
        return self.expert_metadata.get(name)

    def get_all_metadata(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有专家策略的元数据

        Returns:
            专家策略元数据字典，键为名称，值为元数据
        """
        return self.expert_metadata.copy()

    def add_expert(self, name: str, policy: Policy, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        添加专家策略

        Args:
            name: 专家策略名称
            policy: 策略实例
            metadata: 元数据

        Returns:
            是否添加成功
        """
        if name in self.experts:
            logger.warning(f"专家策略 '{name}' 已存在，将被覆盖")

        self.experts[name] = policy

        # 添加元数据
        if metadata is None:
            metadata = {}

        self.expert_metadata[name] = metadata

        logger.info(f"已添加专家策略 '{name}'")
        return True

    def remove_expert(self, name: str) -> bool:
        """
        移除专家策略

        Args:
            name: 专家策略名称

        Returns:
            是否移除成功
        """
        if name not in self.experts:
            logger.warning(f"专家策略 '{name}' 不存在")
            return False

        del self.experts[name]

        # 移除元数据
        if name in self.expert_metadata:
            del self.expert_metadata[name]

        logger.info(f"已移除专家策略 '{name}'")
        return True

    def filter_experts(self, tags: Optional[List[str]] = None,
                      types: Optional[List[str]] = None) -> Dict[str, Policy]:
        """
        根据标签和类型筛选专家策略

        Args:
            tags: 标签列表
            types: 类型列表

        Returns:
            筛选后的专家策略字典
        """
        result = {}

        for name, policy in self.experts.items():
            metadata = self.expert_metadata.get(name, {})

            # 检查标签
            if tags and not any(tag in metadata.get('tags', []) for tag in tags):
                continue

            # 检查类型
            if types and metadata.get('type') not in types:
                continue

            result[name] = policy

        return result

    def save_config(self, path: str) -> bool:
        """
        保存专家池配置

        Args:
            path: 配置文件路径

        Returns:
            是否保存成功
        """
        try:
            # 构建配置
            config = {
                'experts': self.expert_metadata
            }

            # 创建目录
            os.makedirs(os.path.dirname(path), exist_ok=True)

            # 保存配置
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)

            logger.info(f"已保存专家池配置到 '{path}'")
            return True

        except Exception as e:
            logger.error(f"保存专家池配置时出错: {e}")
            return False