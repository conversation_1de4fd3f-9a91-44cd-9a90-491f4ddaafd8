# 🎯 MCP工具智能调用规则体系
*通用化分析框架 - 适用于任何领域和项目*

## 📋 概述

本规则体系采用**分层模块化架构**，提供通用的MCP工具调用框架，确保在任何领域和场景下都能达到最高质量的分析标准。

### 🎯 核心原则
- **MCP工具优先**: 以MCP工具调用为核心分析方法，优先使用工具组合
- **质量至上**: 不考虑计算成本、时间消耗，追求最高分析质量
- **深度解析**: 必须达到根本原因层面，禁止表面分析
- **全维度覆盖**: 全面考虑问题的各个维度和潜在影响
- **通用适配**: 适用于任何领域，支持灵活扩展

### 🏗️ 架构设计
```
┌─────────────────────────────────────────┐
│           质量保证层                      │
│    (.augment-guidelines 集成)           │
├─────────────────────────────────────────┤
│           工具策略层                      │
│      (MCP工具选择与组合)                 │
├─────────────────────────────────────────┤
│           核心规则层                      │
│        (通用规则框架)                    │
├─────────────────────────────────────────┤
│           适配扩展层                      │
│      (领域特定规则扩展)                  │
└─────────────────────────────────────────┘
```

---

## 🏗️ 第一层：核心规则层（通用框架）

### 📋 强制工具调用规则

#### 1. **工具组合优先原则**
```
IF 问题复杂度 >= 中等
THEN 必须使用多工具协作，禁止单一工具处理
```

#### 2. **深度分析强制要求**
```
每个工具调用都必须达到根本原因层面
禁止表面分析和浅层解释
必须提供可验证的深层洞察
```

#### 3. **全维度覆盖检查**
```
必须考虑问题的所有相关维度
必须评估潜在影响和风险
必须提供完整的解决方案
```

#### 4. **质量验证机制**
```
每次分析都必须包含结果验证
必须评估结论的可靠性和适用性
必须提供改进建议和后续步骤
```

### 🎯 通用工具选择矩阵

#### 🔍 分析类问题

| 问题特征 | 主要工具 | 辅助工具 | 质量标准 |
|---------|---------|---------|---------|
| **错误诊断/调试** | `debuggingapproach` | `scientificmethod` | 根因定位准确率 |
| **系统设计/架构** | `visualreasoning` | `collaborativereasoning` | 方案完整性 |
| **性能优化** | `mentalmodel`(pareto) | `debuggingapproach` | 瓶颈识别精度 |
| **技术选型/决策** | `decisionframework` | `mentalmodel`(opportunity_cost) | 方案对比深度 |
| **算法/理论分析** | `scientificmethod` | `mentalmodel`(first_principles) | 理论验证严谨性 |

#### 🧠 思维类问题

| 问题特征 | 主要工具 | 辅助工具 | 质量标准 |
|---------|---------|---------|---------|
| **复杂问题分解** | `sequentialthinking` | `mentalmodel`(first_principles) | 逻辑链完整性 |
| **多角度分析** | `collaborativereasoning` | `structuredargumentation` | 视角覆盖度 |
| **决策制定** | `decisionframework` | `mentalmodel`(opportunity_cost) | 标准全面性 |
| **科学验证** | `scientificmethod` | `metacognitivemonitoring` | 假设验证严谨性 |
| **论证分析** | `structuredargumentation` | `collaborativereasoning` | 论证严密性 |
| **知识评估** | `metacognitivemonitoring` | `sequentialthinking` | 认知准确性 |

#### 🎨 设计类问题

| 问题特征 | 主要工具 | 辅助工具 | 质量标准 |
|---------|---------|---------|---------|
| **概念设计** | `mentalmodel`(first_principles) | `visualreasoning` | 创新度评估 |
| **流程优化** | `visualreasoning` | `mentalmodel`(pareto) | 效率提升度 |
| **方案比较** | `decisionframework` | `collaborativereasoning` | 比较全面性 |

---

## ⚡ 第二层：工具策略层（MCP工具选择与组合）

### 🎯 工具组合策略

#### 🟢 基础分析 (双工具最低要求)
- **触发条件**: 任何需要分析的问题
- **调用策略**: 主工具 + 验证工具
- **强制要求**: 即使简单问题也必须使用至少2个工具
- **示例**:
  - 概念解释 → `mentalmodel`(rubber_duck) + `metacognitivemonitoring`
  - 简单调试 → `debuggingapproach` + `scientificmethod`
  - 基础决策 → `decisionframework` + `mentalmodel`(opportunity_cost)

#### 🟡 深度分析 (多工具协作)
- **触发条件**: 中等复杂度问题，涉及多个维度
- **调用策略**: 主工具 + 多个辅助工具 + 验证工具
- **强制要求**: 必须从至少3个不同角度分析
- **示例**:
  - 系统优化 → `mentalmodel`(pareto) + `debuggingapproach` + `scientificmethod`
  - 架构设计 → `visualreasoning` + `decisionframework` + `collaborativereasoning`
  - 算法改进 → `scientificmethod` + `mentalmodel`(first_principles) + `debuggingapproach`

#### 🔴 全面分析 (多工具协作流程)
- **触发条件**: 高复杂度问题，涉及多个领域和利益相关者
- **调用策略**: 多阶段工具协作流程
- **强制要求**: 必须使用至少4个不同类型的工具
- **示例**:
  - 系统重构 → `metacognitivemonitoring` → `collaborativereasoning` → `decisionframework` → `visualreasoning` → `scientificmethod`
  - 复杂技术决策 → `sequentialthinking` → `collaborativereasoning` → `decisionframework` → `scientificmethod`
  - 创新方案设计 → `mentalmodel`(first_principles) → `visualreasoning` → `collaborativereasoning` → `decisionframework`

---

## 🛡️ 第三层：质量保证层（.augment-guidelines 集成）

### 📋 工作流程质量要求

#### 1. **需求明确化** (来自 .augment-guidelines 第1条)
```
在选择MCP工具前，必须完全理解用户需求
如有歧义，必须要求澄清
禁止基于假设进行工具选择
```

#### 2. **多方案探索** (来自 .augment-guidelines 第2条)
```
对于非平凡问题，必须识别至少2-3种不同的工具组合方案
每种方案必须代表不同的分析角度或方法论
```

#### 3. **批判性评估** (来自 .augment-guidelines 第3条)
```
对每个工具选择方案必须提供平衡分析：
- 优势、效率、与需求的匹配度
- 潜在缺点、风险和复杂性
- 集成计划和潜在陷阱
- 具体的解决方案而非问题抑制
```

#### 4. **推荐理由** (来自 .augment-guidelines 第4条)
```
基于评估结果推荐最佳工具组合
清楚解释推理过程
说明如何缓解风险
```

#### 5. **不确定性承认** (来自 .augment-guidelines 第5条)
```
如果分析后仍不确定最佳方法，必须明确说明
不得伪装确定性
必须识别知识空白或假设
```

#### 6. **研究建议** (来自 .augment-guidelines 第6条)
```
如存在不确定性，必须提出具体研究步骤
建议通过网络搜索、文档查阅等方式解决
```

### 🎯 完成标准定义 (来自 .augment-guidelines 第7条)

#### "完成"的严格定义：
- ✅ **所有明确需求已满足**
- ✅ **分析完全集成且功能完整**
- ✅ **所有关键问题已解决（非仅仅抑制）**
- ✅ **功能已验证（概念性描述测试用例）**
- ✅ **无冗余或未调用的分析内容（除非明确证明必要）**

#### 状态报告要求：
```
如未达到"完成"标准，必须提供精确状态：
- 详细说明已完成的内容
- 明确剩余工作
- 报告遇到的问题/警告
```

### 🏆 质量与透明度优先 (来自 .augment-guidelines 第8条)
```
追求稳健、可维护、安全的解决方案
优先考虑透明度和质量，而非速度或表面完成
对问题保持诚实，特别是在交接时
```

---

## 🎯 第四层：适配扩展层（领域特定规则扩展）

### 📋 通用扩展框架

#### 🔧 技术领域扩展
```
可根据具体技术栈添加专门规则：
- 编程语言特定的调试策略
- 框架特定的架构分析方法
- 平台特定的性能优化规则
```

#### 🏢 业务领域扩展
```
可根据具体业务领域添加专门规则：
- 金融领域的风险评估要求
- 医疗领域的安全性验证
- 教育领域的可理解性要求
```

#### 🎯 项目特定扩展
```
可根据具体项目需求添加专门规则：
- 特定算法的验证要求
- 特定性能指标的优化重点
- 特定质量标准的检查点
```

### 🚀 扩展规则示例模板

#### 领域特定规则模板：
```
### [领域名称]专用规则

#### 强制要求：
IF 问题涉及[领域特征]
THEN 必须包含[特定工具组合]

#### 质量标准：
- [领域特定的质量指标]
- [领域特定的验证方法]
- [领域特定的完成标准]

#### 工具组合建议：
- [主要工具] + [辅助工具] + [验证工具]
```

### 🔄 动态适配机制

1. **规则优先级**
   ```
   领域特定规则 > 通用规则
   项目特定规则 > 领域特定规则
   用户明确要求 > 所有预设规则
   ```

2. **冲突解决**
   ```
   如果规则冲突，优先选择更严格的质量要求
   如果工具冲突，优先选择更全面的分析方法
   如果标准冲突，优先选择更高的完成标准
   ```

3. **扩展验证**
   ```
   新增规则必须与核心原则一致
   新增规则必须提高而非降低质量标准
   新增规则必须具有明确的适用条件
   ```

---

## 📊 第五层：实际应用指南

### 🎯 通用应用流程

#### 步骤1：问题分析
```
1. 明确用户需求（质量保证层要求）
2. 识别问题类型（分析/思维/设计）
3. 评估复杂度级别（基础/深度/全面）
4. 确定质量标准（根据用户要求）
```

#### 步骤2：工具选择
```
1. 根据问题类型选择主工具
2. 根据复杂度确定工具数量
3. 添加验证和辅助工具
4. 检查是否满足强制要求
```

#### 步骤3：执行分析
```
1. 按照工具组合策略执行
2. 确保每个工具达到深度分析要求
3. 进行中间质量检查
4. 必要时动态调整工具组合
```

#### 步骤4：质量验证
```
1. 检查是否满足完成标准
2. 验证分析的全面性和深度
3. 评估结果的可靠性
4. 提供改进建议和后续步骤
```

### 🔄 动态调用规则

#### 1. **质量升级策略**
```
IF 当前分析质量 < 用户要求
THEN 执行以下升级序列：
  1. 添加验证工具
  2. 增加辅助工具
  3. 升级为多工具协作
  4. 启用全面分析模式
```

#### 2. **维度扩展策略**
```
IF 分析过程中发现新的重要维度
THEN 动态添加相应的专门工具
AND 重新评估整体分析策略
```

#### 3. **不确定性处理策略**
```
IF 存在不确定性或知识空白
THEN 必须：
  1. 明确承认不确定性
  2. 提出具体研究建议
  3. 使用多工具交叉验证
  4. 提供多种可能的解决方案
```

### 📋 质量检查清单

#### 工具选择检查：
- ✅ 是否使用了至少2个工具？
- ✅ 工具组合是否覆盖了问题的主要维度？
- ✅ 是否包含了验证机制？
- ✅ 是否满足了强制调用规则？

#### 分析深度检查：
- ✅ 是否达到了根本原因层面？
- ✅ 是否考虑了所有相关维度？
- ✅ 是否评估了潜在影响和风险？
- ✅ 是否提供了可执行的解决方案？

#### 完成标准检查：
- ✅ 所有明确需求是否已满足？
- ✅ 分析是否完全集成且功能完整？
- ✅ 所有关键问题是否已解决？
- ✅ 功能是否已验证？
- ✅ 是否无冗余或未调用的内容？

---

## 🎯 总结

本规则体系通过**分层模块化架构**实现了通用性、质量保证与灵活性的最佳平衡：

### 🏗️ 架构优势
- **核心规则层**：提供通用的工具选择框架，适用于任何领域
- **工具策略层**：强化MCP工具组合使用，确保深度分析
- **质量保证层**：集成.augment-guidelines要求，确保工作流程质量
- **适配扩展层**：支持领域特定规则扩展，保持灵活性
- **应用指南层**：提供具体可操作的实施指导

### 🎯 核心特色
- **MCP工具优先**：以工具调用为核心分析方法
- **强制组合使用**：禁止单一工具处理复杂问题
- **深度分析要求**：必须达到根本原因层面
- **全维度覆盖**：确保分析的完整性和全面性
- **质量验证机制**：多重检查确保最高标准

### 🚀 适用范围
- **通用适配**：适用于任何技术领域和业务场景
- **可扩展性**：支持添加领域特定和项目特定规则
- **质量保证**：确保始终达到最高分析质量标准
- **持续改进**：支持规则体系的动态优化和完善

这套规则体系将指导AI在任何场景下都能选择最优的MCP工具组合，确保分析质量始终达到最高标准，同时保持对不同领域和项目需求的适应性。
