# MCTS日志增强技术规范

**制定时间**: 2025-01-02  
**制定团队**: 深度研究专家 Wendy & 构架师 Timmy  
**适用范围**: 斗地主AI训练项目MCTS实现  

## 🎯 设计目标

### 核心目标
1. **详细追踪**: 完整记录MCTS搜索过程的每个关键步骤
2. **性能监控**: 实时监控搜索性能和资源使用情况
3. **调试友好**: 提供清晰、结构化的调试信息
4. **可配置性**: 支持不同级别的日志详细程度
5. **低开销**: 日志功能对性能影响<5%

### 设计原则
- **结构化日志**: 使用统一的日志格式和字段
- **分级记录**: 支持DEBUG、INFO、WARNING、ERROR级别
- **上下文感知**: 包含游戏状态、玩家ID、时间戳等上下文信息
- **可选启用**: 生产环境可完全关闭详细日志
- **线程安全**: 支持多线程环境下的安全日志记录

## 📋 日志分类体系

### 1. UCB计算日志 (级别: DEBUG)
**目的**: 追踪UCB分数计算过程，帮助调试选择策略

**记录内容**:
- 父节点访问次数
- 子节点访问次数和价值
- 先验概率
- UCB分数计算过程
- 最终选择的动作

### 2. 节点扩展日志 (级别: DEBUG)
**目的**: 监控节点扩展过程和策略网络输出

**记录内容**:
- 扩展前节点状态
- 策略网络输出概率分布
- 新增子节点数量
- 扩展耗时
- 内存使用变化

### 3. 搜索路径日志 (级别: DEBUG)
**目的**: 完整追踪搜索路径，便于分析搜索行为

**记录内容**:
- 完整搜索路径
- 每个节点的状态信息
- 路径深度
- 路径选择原因

### 4. 模拟结果日志 (级别: DEBUG)
**目的**: 记录模拟过程和结果，分析价值估计准确性

**记录内容**:
- 模拟起始状态
- 模拟结果价值
- 模拟耗时
- 信念状态使用情况

### 5. 性能统计日志 (级别: INFO)
**目的**: 监控整体搜索性能

**记录内容**:
- 总搜索时间
- 模拟次数统计
- 内存使用峰值
- 平均每次模拟耗时
- 搜索树大小统计

## 🏗️ 技术架构设计

### 日志模块架构
```
cardgame_ai/algorithms/mcts/logging/
├── __init__.py
├── mcts_logger.py          # 主日志器类
├── formatters.py           # 日志格式化器
├── handlers.py             # 日志处理器
├── filters.py              # 日志过滤器
└── config.py               # 日志配置管理
```

### 核心组件设计

#### 1. MCTSLogger类
```python
class MCTSLogger:
    """MCTS专用日志器"""

    def __init__(self, level='INFO', enable_debug=False):
        self.level = level
        self.enable_debug = enable_debug
        self.session_id = self._generate_session_id()

    def log_ucb_calculation(self, node, children_scores):
        """记录UCB计算过程"""

    def log_node_expansion(self, node, policy_output, expansion_time):
        """记录节点扩展"""

    def log_search_path(self, path, depth):
        """记录搜索路径"""

    def log_simulation_result(self, state, value, sim_time):
        """记录模拟结果"""

    def log_performance_stats(self, stats):
        """记录性能统计"""
```

#### 2. 日志格式化器
```python
class MCTSFormatter:
    """MCTS日志格式化器"""

    def format_ucb_log(self, timestamp, session_id, data):
        """格式化UCB计算日志"""
        return {
            'timestamp': timestamp,
            'session_id': session_id,
            'type': 'ucb_calculation',
            'data': data
        }
```

## 📝 日志格式规范

### 统一日志格式
```json
{
    "timestamp": "2025-01-02T10:30:45.123Z",
    "session_id": "mcts_20250102_103045_abc123",
    "level": "DEBUG",
    "type": "ucb_calculation",
    "player_id": "P0",
    "game_state": "bidding_phase",
    "data": {
        "parent_visits": 150,
        "children": [
            {
                "action": 42,
                "visits": 25,
                "value": 0.65,
                "prior": 0.12,
                "ucb_score": 0.78
            }
        ],
        "selected_action": 42,
        "selection_reason": "highest_ucb"
    }
}
```

## 📋 分步骤实施路线图

### 第1天: 基础日志框架搭建

#### 上午任务 (4小时)
**任务1.1: 创建日志模块结构**

```bash
# 创建日志模块目录
mkdir -p cardgame_ai/algorithms/mcts/logging
touch cardgame_ai/algorithms/mcts/logging/__init__.py
touch cardgame_ai/algorithms/mcts/logging/mcts_logger.py
touch cardgame_ai/algorithms/mcts/logging/formatters.py
touch cardgame_ai/algorithms/mcts/logging/config.py
```

**任务1.2: 实现基础MCTSLogger类**

```python
# cardgame_ai/algorithms/mcts/logging/mcts_logger.py
import time
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

class MCTSLogger:
    """
    MCTS专用日志器

    提供结构化的MCTS搜索过程日志记录功能，支持：
    - UCB计算过程追踪
    - 节点扩展详情记录
    - 搜索路径完整追踪
    - 性能统计监控
    """

    def __init__(self, level: str = 'INFO', enable_debug: bool = False):
        self.level = level
        self.enable_debug = enable_debug
        self.session_id = self._generate_session_id()
        self.logger = self._setup_logger()

    def _generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"mcts_{timestamp}_{hash(time.time()) % 10000:04d}"
```

#### 下午任务 (4小时)
**任务1.3: 实现UCB计算日志功能**

```python
def log_ucb_calculation(self,
                       parent_node: Any,
                       children_scores: List[Dict],
                       selected_action: int,
                       game_context: Optional[Dict] = None):
    """
    记录UCB计算过程

    Args:
        parent_node: 父节点对象
        children_scores: 子节点UCB分数列表
        selected_action: 最终选择的动作
        game_context: 游戏上下文信息
    """
    if not self.enable_debug:
        return

    log_data = {
        'timestamp': datetime.now().isoformat(),
        'session_id': self.session_id,
        'level': 'DEBUG',
        'type': 'ucb_calculation',
        'data': {
            'parent_visits': parent_node.visit_count,
            'children_count': len(children_scores),
            'children_scores': children_scores,
            'selected_action': selected_action,
            'game_context': game_context or {}
        }
    }

    self.logger.debug(json.dumps(log_data, ensure_ascii=False))
```

**验收标准**:
- [ ] 日志模块结构创建完成
- [ ] MCTSLogger基础类实现
- [ ] UCB计算日志功能可用
- [ ] 单元测试通过
